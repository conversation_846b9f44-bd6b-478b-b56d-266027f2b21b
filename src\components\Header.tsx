'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import Image from 'next/image'
import { useState, useEffect } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { getLastPlayedWorld } from '@/utils/world-utils'

export default function Header() {
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { data: session, status } = useSession()
  const [lastPlayedWorldId, setLastPlayedWorldId] = useState<string | null>(null)

  const isLoading = status === 'loading'
  const isAuthenticated = status === 'authenticated'

  // Get last played world ID from localStorage
  useEffect(() => {
    const lastWorldId = getLastPlayedWorld()
    setLastPlayedWorldId(lastWorldId)
  }, [])

  const isActive = (path: string) => {
    // Special case for the Play link
    if (path.startsWith('/world/') && pathname.startsWith('/world/')) {
      return true
    }
    return pathname === path
  }

  const links = [
    { href: '/', label: 'Home' },
    { href: '/worlds', label: 'Worlds' },
  ]

  // Add Play link if there's a last played world
  if (lastPlayedWorldId) {
    links.push({ href: `/world/${lastPlayedWorldId}`, label: 'Play' })
  }

  return (
    <header className="bg-background-darker border-b border-gray-800 py-4 sticky top-0 z-50">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-10 h-10 relative">
            <Image
              src="/images/d20-icon.svg"
              alt="SoloQuest Logo"
              width={40}
              height={40}
              className="object-contain"
            />
          </div>
          <span className="text-xl font-medieval text-primary">SoloQuest</span>
        </Link>

        {/* Mobile menu button */}
        <button
          className="md:hidden text-white"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M3.75 6.75h16.5M3.75 12h16.5M3.75 17.25h16.5"} />
          </svg>
        </button>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          {links.map((link) => (
            <NavLink
              key={link.href}
              href={link.href}
              text={link.label}
              isActive={isActive(link.href)}
            />
          ))}

          {isLoading ? (
            <div className="w-24 h-10 bg-background-darker animate-pulse rounded-lg"></div>
          ) : isAuthenticated ? (
            <Link
              href="/profile"
              className="flex items-center space-x-2 text-text-secondary hover:text-white transition-colors"
            >
              <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white overflow-hidden">
                {session?.user?.image ? (
                  <Image
                    src={session.user.image}
                    alt={session.user.name || 'User'}
                    width={32}
                    height={32}
                    className="object-cover"
                  />
                ) : (
                  <span className="text-sm font-bold">{session?.user?.name?.charAt(0) || 'U'}</span>
                )}
              </div>
              <span className="text-sm">{session?.user?.name?.split(' ')[0] || 'User'}</span>
            </Link>
          ) : (
            <Link
              href="/signup"
              className="fantasy-button text-sm"
            >
              Sign Up
            </Link>
          )}
        </nav>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <nav className="md:hidden bg-background-darker border-t border-gray-800 mt-4 py-2">
          <div className="container mx-auto px-4 flex flex-col space-y-3">
            {links.map((link) => (
              <NavLink
                key={link.href}
                href={link.href}
                text={link.label}
                isActive={isActive(link.href)}
              />
            ))}

            {isLoading ? (
              <div className="w-full h-10 bg-background-darker animate-pulse rounded-lg"></div>
            ) : isAuthenticated ? (
              <>
                <Link
                  href="/profile"
                  className="text-sm font-medium py-2 px-4 rounded-md text-text-secondary hover:bg-primary/10 hover:text-white transition-colors"
                >
                  Profile
                </Link>
                <Link
                  href="/worlds"
                  className="text-sm font-medium py-2 px-4 rounded-md text-text-secondary hover:bg-primary/10 hover:text-white transition-colors"
                >
                  My Worlds
                </Link>
                <button
                  onClick={() => signOut({ callbackUrl: '/' })}
                  className="text-sm font-medium py-2 px-4 text-left rounded-md text-accent hover:bg-accent/10 transition-colors"
                >
                  Sign Out
                </button>
              </>
            ) : (
              <Link
                href="/signup"
                className="fantasy-button text-sm w-full text-center"
              >
                Sign Up
              </Link>
            )}
          </div>
        </nav>
      )}
    </header>
  )
}

function NavLink({ href, text, isActive }: { href: string; text: string; isActive: boolean }) {
  return (
    <Link
      href={href}
      className={`text-sm font-medium transition-colors duration-200 hover:text-primary ${
        isActive ? 'text-primary border-b-2 border-primary pb-1' : 'text-text-secondary'
      }`}
    >
      {text}
    </Link>
  )
}
